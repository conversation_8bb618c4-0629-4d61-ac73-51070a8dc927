import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:geolocator/geolocator.dart';  // Temporarily disabled
import '../models/behavioral_data_model.dart';

class FirebaseService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  Future<void> uploadUsageTimeOfDay(String userId, String timeOfDay) async {
  final timestamp = DateTime.now().millisecondsSinceEpoch;

  await _firestore
      .collection('users')
      .doc(userId)
      .collection('usage_time')
      .doc('$timestamp')
      .set({
    'timeOfDay': timeOfDay,
    'timestamp': timestamp,
  });
  }

  Future<void> logPasteAttempt(String userId) async {
  try {
    final timestamp = DateTime.now().toIso8601String();
    await FirebaseFirestore.instance
        .collection('users')
        .doc(userId)
        .collection('paste_attempts')
        .doc(timestamp)
        .set({
      'timestamp': timestamp,
      'field': 'password',
      'detected': true,
    });
  } catch (e) {
    // Failed to log paste attempt - continue
  }
}
  Future<void> uploadUserLocation(String userId, double latitude, double longitude, bool isWithinZone) async {
  final timestamp = DateTime.now().millisecondsSinceEpoch;

  await _firestore.collection('users').doc(userId).collection('sessions').doc('$timestamp').set({
    'location': {
      'latitude': latitude,
      'longitude': longitude,
      'withinZone': isWithinZone,
      'timestamp': timestamp,
    },
  }, SetOptions(merge: true));
}


  Future<void> uploadBehavioralData(
    String userId,
    BehavioralData data, {
    // Position? geolocation,  // Temporarily disabled
    Map<String, dynamic>? geolocation,
  }) async {
  final trimmedId = userId.trim();
  if (trimmedId.isEmpty) return;

  try {
    final sessionId = DateTime.now().toIso8601String();
    final Map<String, dynamic> payload = {
      ...data.toJson(),
      'timestamp': sessionId,
    };

    if (geolocation != null) {
      payload['geolocation'] = geolocation;
    }

    // Upload to user's session
    await FirebaseFirestore.instance
        .collection('users')
        .doc(trimmedId)
        .collection('sessions')
        .doc(sessionId)
        .set(payload);

    // ✅ Optional: Also upload to top-level user doc
    if (geolocation != null) {
      await FirebaseFirestore.instance
          .collection('users')
          .doc(trimmedId)
          .set({
        'geolocation': geolocation,
        'updatedAt': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));
    }
  } catch (e) {
    // Upload behavioral data failed - continue
  }
}


  void uploadForegroundDuration(String userId, int duration) {}

  /// Send emergency alert to security system
  Future<void> sendEmergencyAlert(String userId, String alertType, Map<String, dynamic> details) async {
    try {
      final timestamp = DateTime.now().toIso8601String();
      
      await _firestore
          .collection('emergency_alerts')
          .doc(timestamp)
          .set({
        'userId': userId,
        'alertType': alertType,
        'details': details,
        'timestamp': timestamp,
        'serverTimestamp': FieldValue.serverTimestamp(),
        'status': 'active',
      });
      
      // Also update user's alert history
      await _firestore
          .collection('users')
          .doc(userId)
          .collection('alerts')
          .doc(timestamp)
          .set({
        'alertType': alertType,
        'details': details,
        'timestamp': timestamp,
        'serverTimestamp': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      // Failed to send emergency alert - continue
    }
  }
}
