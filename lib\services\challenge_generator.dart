import 'dart:math';
import 'dart:async';
import 'dart:ui';
import '../models/behavioral_data_model.dart';
import '../services/security_service.dart';

class ChallengeGenerator {
  static final ChallengeGenerator _instance = ChallengeGenerator._internal();
  factory ChallengeGenerator() => _instance;
  ChallengeGenerator._internal();

  final Random _random = Random();
  Timer? _challengeTimer;
  
  /// 🚀 HACKATHON FEATURE: Generate adaptive challenge based on real-time behavioral risk
  static AdaptiveChallenge generateAdaptiveChallenge(double riskScore, BehavioralData currentData) {
    final challengeType = _determineChallengeType(riskScore);
    final currentHour = DateTime.now().hour;
    
    switch (challengeType) {
      case ChallengeType.behaviorPattern:
        return _generateBehaviorPatternChallenge(currentData);
      case ChallengeType.typingDynamics:
        return _generateTypingDynamicsChallenge(currentData);
      case ChallengeType.gestureVerification:
        return _generateGestureChallenge(currentData);
      case ChallengeType.contextualAwareness:
        return _generateContextualChallenge(currentData, currentHour);
      default:
        return _generateTimeAwarenessChallenge(currentHour);
    }
  }

  static ChallengeType _determineChallengeType(double riskScore) {
    if (riskScore < 0.3) {
      // Low risk - simple contextual challenges
      return [ChallengeType.contextualAwareness, ChallengeType.timeAwareness][_random.nextInt(2)];
    } else if (riskScore < 0.7) {
      // Medium risk - behavioral pattern challenges
      return [ChallengeType.behaviorPattern, ChallengeType.typingDynamics][_random.nextInt(2)];
    } else {
      // High risk - advanced biometric challenges
      return [ChallengeType.gestureVerification, ChallengeType.typingDynamics][_random.nextInt(2)];
    }
  }

  static AdaptiveChallenge _generateBehaviorPatternChallenge(BehavioralData data) {
    final typingSpeed = data.typingSpeedKPM;
    final sessionDuration = DateTime.now().millisecondsSinceEpoch - data.sessionStartTime;
    
    final questions = [
      "How would you describe your current typing speed?",
      "How are you holding your device right now?",
      "Are you typing differently than usual?",
    ];
    
    final optionSets = [
      ["Faster than normal", "Normal speed", "Slower than normal", "Very different"],
      ["One hand", "Both hands", "Switching hands", "Device on surface"],
      ["Yes, very different", "Slightly different", "Same as usual", "Not sure"],
    ];
    
    final questionIndex = _random.nextInt(questions.length);
    final correctAnswer = _determineBehaviorAnswer(data, questions[questionIndex]);
    
    return AdaptiveChallenge(
      id: 'behavior_${DateTime.now().millisecondsSinceEpoch}',
      type: ChallengeType.behaviorPattern,
      question: questions[questionIndex],
      options: optionSets[questionIndex],
      correctAnswerIndex: correctAnswer,
      difficulty: DifficultyLevel.medium,
      timeLimit: 25,
      explanation: "Based on your current behavioral patterns",
      riskScore: 0.5,
    );
  }

  static AdaptiveChallenge _generateTypingDynamicsChallenge(BehavioralData data) {
    final phrases = [
      "Canara Bank secure authentication",
      "Banking with behavioral security", 
      "Trust and innovation in fintech",
      "Digital banking made secure",
    ];
    
    final selectedPhrase = phrases[_random.nextInt(phrases.length)];
    
    return AdaptiveChallenge(
      id: 'typing_${DateTime.now().millisecondsSinceEpoch}',
      type: ChallengeType.typingDynamics,
      question: "Type the following phrase with your natural rhythm:",
      typingTarget: selectedPhrase,
      options: [],
      correctAnswerIndex: -1,
      difficulty: DifficultyLevel.high,
      timeLimit: 45,
      explanation: "Verify your unique typing pattern",
      requiresTyping: true,
      riskScore: 0.8,
    );
  }

  static AdaptiveChallenge _generateGestureChallenge(BehavioralData data) {
    final gestures = [
      "Draw a circle clockwise on the screen",
      "Swipe in this pattern: ↑→↓←",
      "Tap the four corners in order: ↖ ↗ ↘ ↙",
      "Draw the letter 'C' for Canara Bank",
    ];
    
    final selectedGesture = gestures[_random.nextInt(gestures.length)];
    
    return AdaptiveChallenge(
      id: 'gesture_${DateTime.now().millisecondsSinceEpoch}',
      type: ChallengeType.gestureVerification,
      question: "Perform this gesture pattern:",
      gestureInstruction: selectedGesture,
      options: [],
      correctAnswerIndex: -1,
      difficulty: DifficultyLevel.high,
      timeLimit: 30,
      explanation: "Verify your unique gesture signature",
      requiresGesture: true,
      riskScore: 0.9,
    );
  }

  static AdaptiveChallenge _generateContextualChallenge(BehavioralData data, int currentHour) {
    final questions = [
      "What's your current environment?",
      "How are you positioned while banking?",
      "What's your current focus level?",
    ];
    
    final optionSets = [
      ["Quiet private space", "Busy environment", "Public place", "Moving/traveling"],
      ["Sitting comfortably", "Standing", "Walking", "Lying down"],
      ["Fully focused", "Somewhat focused", "Distracted", "Multitasking"],
    ];
    
    final questionIndex = _random.nextInt(questions.length);
    
    return AdaptiveChallenge(
      id: 'context_${DateTime.now().millisecondsSinceEpoch}',
      type: ChallengeType.contextualAwareness,
      question: questions[questionIndex],
      options: optionSets[questionIndex],
      correctAnswerIndex: -1, // No wrong answer for contextual
      difficulty: DifficultyLevel.low,
      timeLimit: 20,
      explanation: "Help us understand your current context",
      isContextual: true,
      riskScore: 0.2,
    );
  }

  static AdaptiveChallenge _generateTimeAwarenessChallenge(int currentHour) {
    final timeOfDay = _getTimeOfDay(currentHour);
    
    return AdaptiveChallenge(
      id: 'time_${DateTime.now().millisecondsSinceEpoch}',
      type: ChallengeType.timeAwareness,
      question: "When do you usually access your bank account?",
      options: ["Morning (6-12)", "Afternoon (12-18)", "Evening (18-24)", "Night (0-6)"],
      correctAnswerIndex: _getTimeIndex(currentHour),
      difficulty: DifficultyLevel.low,
      timeLimit: 15,
      explanation: "Based on your usual banking hours",
      riskScore: 0.1,
    );
  }

  static int _determineBehaviorAnswer(BehavioralData data, String question) {
    if (question.contains("typing speed")) {
      final speed = data.typingSpeedKPM;
      if (speed > 70) return 0; // Faster than normal
      if (speed > 40) return 1; // Normal speed
      return 2; // Slower than normal
    }
    
    if (question.contains("holding")) {
      // Analyze tap distribution to guess grip
      if (data.tapPositions.length > 3) {
        final avgX = data.tapPositions.map((p) => p.dx).reduce((a, b) => a + b) / data.tapPositions.length;
        return avgX < 200 ? 0 : 1; // Simple one-hand vs two-hand heuristic
      }
    }
    
    return _random.nextInt(3); // Default random
  }

  static String _getTimeOfDay(int hour) {
    if (hour >= 6 && hour < 12) return "Morning";
    if (hour >= 12 && hour < 18) return "Afternoon";
    if (hour >= 18 && hour < 24) return "Evening";
    return "Night";
  }

  static int _getTimeIndex(int hour) {
    if (hour >= 6 && hour < 12) return 0; // Morning
    if (hour >= 12 && hour < 18) return 1; // Afternoon
    if (hour >= 18 && hour < 24) return 2; // Evening
    return 3; // Night
  }

  /// 🔥 HACKATHON FEATURE: Evaluate challenge response with ML-style analysis
  static Future<ChallengeResult> evaluateResponse(
    AdaptiveChallenge challenge, 
    String userResponse, 
    BehavioralData responseData
  ) async {
    final startTime = DateTime.now();
    var score = 0.0;
    var confidence = 0.0;
    var feedback = "";
    var behavioralConsistency = 0.0;

    switch (challenge.type) {
      case ChallengeType.behaviorPattern:
      case ChallengeType.contextualAwareness:
      case ChallengeType.timeAwareness:
        final responseIndex = int.tryParse(userResponse) ?? -1;
        if (challenge.isContextual) {
          score = 1.0;
          confidence = 0.8;
          feedback = "Context information recorded";
        } else if (responseIndex == challenge.correctAnswerIndex) {
          score = 1.0;
          confidence = 0.9;
          feedback = "Response matches your behavioral pattern";
        } else {
          score = 0.3; // Partial credit for attempting
          confidence = 0.6;
          feedback = "Response differs from expected pattern";
        }
        break;

      case ChallengeType.typingDynamics:
        final result = await _evaluateTypingPattern(challenge.typingTarget ?? "", userResponse, responseData);
        score = result['score'] ?? 0.0;
        confidence = result['confidence'] ?? 0.5;
        feedback = result['feedback'] ?? "Typing pattern analyzed";
        behavioralConsistency = result['consistency'] ?? 0.5;
        break;

      case ChallengeType.gestureVerification:
        final result = await _evaluateGesturePattern(challenge.gestureInstruction ?? "", responseData);
        score = result['score'] ?? 0.0;
        confidence = result['confidence'] ?? 0.5;
        feedback = result['feedback'] ?? "Gesture pattern analyzed";
        behavioralConsistency = result['consistency'] ?? 0.5;
        break;
    }

    final responseTime = DateTime.now().difference(startTime).inMilliseconds;
    
    // Advanced behavioral analysis
    final timeScore = _evaluateResponseTime(responseTime, challenge.timeLimit);
    final overallScore = (score * 0.7) + (timeScore * 0.2) + (behavioralConsistency * 0.1);
    
    // Record for ML training
    await _recordChallengeResponse(challenge, userResponse, overallScore, responseTime, responseData);

    return ChallengeResult(
      challengeId: challenge.id,
      success: overallScore >= 0.6,
      score: overallScore,
      confidence: confidence,
      responseTime: responseTime,
      feedback: feedback,
      behavioralConsistency: behavioralConsistency,
      riskReduction: overallScore * (challenge.riskScore * 0.5),
    );
  }

  static Future<Map<String, dynamic>> _evaluateTypingPattern(String target, String typed, BehavioralData data) async {
    // Text similarity
    final similarity = _calculateTextSimilarity(target.toLowerCase(), typed.toLowerCase());
    
    // Typing rhythm analysis
    final rhythmScore = data.keystrokeTimes.isNotEmpty ? _analyzeTypingRhythm(data.keystrokeTimes) : 0.5;
    
    // Speed consistency
    final speedScore = _analyzeTypingSpeed(data.typingSpeedKPM);
    
    final overallScore = (similarity * 0.5) + (rhythmScore * 0.3) + (speedScore * 0.2);
    
    return {
      'score': overallScore,
      'confidence': similarity > 0.8 ? 0.9 : 0.7,
      'feedback': _getTypingFeedback(similarity, rhythmScore),
      'consistency': rhythmScore,
    };
  }

  static Future<Map<String, dynamic>> _evaluateGesturePattern(String instruction, BehavioralData data) async {
    final gestureScore = data.tapPositions.isNotEmpty ? _analyzeGesturePattern(data.tapPositions, instruction) : 0.5;
    final smoothness = _analyzeGestureSmoothness(data.tapPositions);
    
    final overallScore = (gestureScore * 0.7) + (smoothness * 0.3);
    
    return {
      'score': overallScore,
      'confidence': 0.8,
      'feedback': gestureScore > 0.7 ? "Gesture pattern recognized" : "Gesture analysis complete",
      'consistency': smoothness,
    };
  }

  static double _calculateTextSimilarity(String a, String b) {
    if (a == b) return 1.0;
    if (a.isEmpty || b.isEmpty) return 0.0;
    
    final maxLength = a.length > b.length ? a.length : b.length;
    final distance = _levenshteinDistance(a, b);
    return 1.0 - (distance / maxLength);
  }

  static int _levenshteinDistance(String a, String b) {
    final matrix = List.generate(a.length + 1, (i) => List.filled(b.length + 1, 0));
    
    for (int i = 0; i <= a.length; i++) matrix[i][0] = i;
    for (int j = 0; j <= b.length; j++) matrix[0][j] = j;
    
    for (int i = 1; i <= a.length; i++) {
      for (int j = 1; j <= b.length; j++) {
        final cost = a[i - 1] == b[j - 1] ? 0 : 1;
        matrix[i][j] = [
          matrix[i - 1][j] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j - 1] + cost,
        ].reduce((a, b) => a < b ? a : b);
      }
    }
    
    return matrix[a.length][b.length];
  }

  static double _analyzeTypingRhythm(List<int> keystrokeTimes) {
    if (keystrokeTimes.length < 3) return 0.5;
    
    final intervals = <int>[];
    for (int i = 1; i < keystrokeTimes.length; i++) {
      intervals.add(keystrokeTimes[i] - keystrokeTimes[i - 1]);
    }
    
    final mean = intervals.reduce((a, b) => a + b) / intervals.length;
    final variance = intervals.map((x) => (x - mean) * (x - mean)).reduce((a, b) => a + b) / intervals.length;
    
    // Consistent rhythm scores higher
    return 1.0 / (1.0 + variance / 10000);
  }

  static double _analyzeTypingSpeed(double speed) {
    // Normalize speed score (30-80 WPM is typical)
    if (speed >= 30 && speed <= 80) return 1.0;
    if (speed >= 20 && speed <= 100) return 0.8;
    if (speed >= 10 && speed <= 120) return 0.6;
    return 0.4;
  }

  static double _analyzeGesturePattern(List<Offset> positions, String instruction) {
    if (positions.length < 3) return 0.5;
    
    // Simple pattern recognition based on instruction
    if (instruction.contains("circle")) {
      return _detectCircularMotion(positions);
    } else if (instruction.contains("swipe")) {
      return _detectSwipePattern(positions);
    } else if (instruction.contains("corners")) {
      return _detectCornerTaps(positions);
    } else if (instruction.contains("letter")) {
      return _detectLetterShape(positions);
    }
    
    return 0.7; // Default for unrecognized patterns
  }

  static double _analyzeGestureSmoothness(List<Offset> positions) {
    if (positions.length < 3) return 0.5;
    
    var smoothness = 0.0;
    for (int i = 1; i < positions.length - 1; i++) {
      final dx1 = positions[i].dx - positions[i - 1].dx;
      final dy1 = positions[i].dy - positions[i - 1].dy;
      final dx2 = positions[i + 1].dx - positions[i].dx;
      final dy2 = positions[i + 1].dy - positions[i].dy;
      
      final angle1 = atan2(dy1, dx1);
      final angle2 = atan2(dy2, dx2);
      final angleDiff = (angle2 - angle1).abs();
      
      smoothness += angleDiff < pi / 4 ? 1.0 : 0.0;
    }
    
    return smoothness / (positions.length - 2);
  }

  static double _detectCircularMotion(List<Offset> positions) {
    if (positions.length < 8) return 0.5;
    
    // Calculate center
    final centerX = positions.map((p) => p.dx).reduce((a, b) => a + b) / positions.length;
    final centerY = positions.map((p) => p.dy).reduce((a, b) => a + b) / positions.length;
    
    // Check if points form a circle
    final center = Offset(centerX, centerY);
    final distances = positions.map((p) => (p - center).distance).toList();
    final avgRadius = distances.reduce((a, b) => a + b) / distances.length;
    
    // Calculate variance in radius
    final radiusVariance = distances.map((d) => (d - avgRadius) * (d - avgRadius)).reduce((a, b) => a + b) / distances.length;
    
    // Lower variance = more circular
    return 1.0 / (1.0 + radiusVariance / 1000);
  }

  static double _detectSwipePattern(List<Offset> positions) {
    if (positions.length < 4) return 0.5;
    
    // Check for directional consistency
    var consistentDirection = 0.0;
    for (int i = 1; i < positions.length; i++) {
      final dx = positions[i].dx - positions[i - 1].dx;
      final dy = positions[i].dy - positions[i - 1].dy;
      
      // Check if movement is predominantly in one direction
      if (dx.abs() > dy.abs() || dy.abs() > dx.abs()) {
        consistentDirection += 1.0;
      }
    }
    
    return consistentDirection / (positions.length - 1);
  }

  static double _detectCornerTaps(List<Offset> positions) {
    if (positions.length < 4) return 0.5;
    
    // Simple heuristic: check if taps are near screen corners
    var cornerHits = 0;
    for (final pos in positions) {
      if ((pos.dx < 100 || pos.dx > 300) && (pos.dy < 100 || pos.dy > 600)) {
        cornerHits++;
      }
    }
    
    return cornerHits / positions.length.toDouble();
  }

  static double _detectLetterShape(List<Offset> positions) {
    if (positions.length < 5) return 0.5;
    
    // Simple shape detection for letter patterns
    final bounds = _calculateBoundingBox(positions);
    final aspectRatio = bounds.width / bounds.height;
    
    // Letter shapes typically have specific aspect ratios
    return aspectRatio > 0.5 && aspectRatio < 2.0 ? 0.8 : 0.6;
  }

  static Rect _calculateBoundingBox(List<Offset> positions) {
    var minX = positions.first.dx;
    var maxX = positions.first.dx;
    var minY = positions.first.dy;
    var maxY = positions.first.dy;
    
    for (final pos in positions) {
      minX = minX < pos.dx ? minX : pos.dx;
      maxX = maxX > pos.dx ? maxX : pos.dx;
      minY = minY < pos.dy ? minY : pos.dy;
      maxY = maxY > pos.dy ? maxY : pos.dy;
    }
    
    return Rect.fromLTRB(minX, minY, maxX, maxY);
  }

  static double _evaluateResponseTime(int responseTime, int timeLimit) {
    final limitMs = timeLimit * 1000;
    if (responseTime <= limitMs) {
      return 1.0 - (responseTime / limitMs * 0.5); // Faster response scores higher
    } else {
      return 0.3; // Partial credit for late response
    }
  }

  static String _getTypingFeedback(double similarity, double rhythm) {
    if (similarity > 0.9 && rhythm > 0.8) return "Perfect typing match!";
    if (similarity > 0.8) return "Good typing accuracy";
    if (rhythm > 0.8) return "Consistent typing rhythm";
    return "Typing pattern analyzed";
  }

  static Future<void> _recordChallengeResponse(AdaptiveChallenge challenge, String response, double score, int responseTime, BehavioralData data) async {
    await SecurityService.recordBehavioralData({
      'challenge_id': challenge.id,
      'challenge_type': challenge.type.toString(),
      'difficulty': challenge.difficulty.toString(),
      'user_response': response,
      'challenge_score': score,
      'response_time_ms': responseTime,
      'risk_score': challenge.riskScore,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'behavioral_context': data.toJson(),
      'time_of_day': DateTime.now().hour,
      'day_of_week': DateTime.now().weekday,
    });
  }

  // 🚀 HACKATHON: Generate training data for ML model
  static List<AdaptiveChallenge> generateTrainingSet(int count) {
    final challenges = <AdaptiveChallenge>[];
    for (int i = 0; i < count; i++) {
      final riskScore = _random.nextDouble();
      final mockData = _generateMockBehavioralData();
      challenges.add(generateAdaptiveChallenge(riskScore, mockData));
    }
    return challenges;
  }

  static BehavioralData _generateMockBehavioralData() {
    final data = BehavioralData();
    final baseTime = DateTime.now().millisecondsSinceEpoch;
    
    // Generate realistic keystroke data
    for (int i = 0; i < 15; i++) {
      data.addKeystroke(baseTime + i * (100 + _random.nextInt(200)));
    }
    
    // Generate tap data
    for (int i = 0; i < 8; i++) {
      data.addTapData(
        50 + _random.nextDouble() * 100,
        Offset(_random.nextDouble() * 400, _random.nextDouble() * 800),
      );
    }
    
    return data;
  }
        ['Daily', 'Weekly', 'Monthly', 'Rarely'],
        ['Check balance', 'Transfer money', 'Pay bills', 'All of the above'],
        ['Yes, planned transactions', 'No, just checking', 'Maybe, depends on balance'],
      ],
    ),
    ChallengeCategory(
      id: 'device_context',
      name: 'Device Context',
      questions: [
        'Are you using your usual device for banking?',
        'Is this device shared with anyone else?',
        'When did you last update your banking app?',
      ],
      options: [
        ['Yes, my usual device', 'No, different device', 'One of my devices'],
        ['No, only I use it', 'Yes, shared device', 'Sometimes shared'],
        ['Recently (this week)', 'A while ago', 'I don\'t remember'],
      ],
    ),
  ];

  /// Generate a contextual challenge based on risk score
  ChallengeData generateChallenge(double riskScore, Map<String, dynamic> context) {
    // Higher risk = more specific challenges
    final category = _selectCategoryByRisk(riskScore);
    final questionIndex = _random.nextInt(category.questions.length);
    
    return ChallengeData(
      id: '${category.id}_${DateTime.now().millisecondsSinceEpoch}',
      category: category.id,
      question: category.questions[questionIndex],
      options: category.options[questionIndex],
      correctAnswerIndex: _determineCorrectAnswer(category, questionIndex, context),
      riskScore: riskScore,
      timestamp: DateTime.now(),
      timeoutSeconds: _getTimeoutByRisk(riskScore),
    );
  }

  /// Generate a simple challenge with default parameters
  ChallengeData generateSimpleChallenge() {
    return generateChallenge(0.5, {});
  }

  /// Generate multiple challenges for high-risk situations
  Future<List<ChallengeData>> generateChallengeSequence(double riskScore, Map<String, dynamic> context) async {
    if (riskScore < 0.3) return [];
    
    final challenges = <ChallengeData>[];
    final numChallenges = riskScore > 0.8 ? 3 : riskScore > 0.6 ? 2 : 1;
    
    for (int i = 0; i < numChallenges; i++) {
      challenges.add(generateChallenge(riskScore, context));
      // Ensure variety in challenge types
      if (i < numChallenges - 1) {
        await Future.delayed(Duration(milliseconds: 100));
      }
    }
    
    return challenges;
  }

  /// Schedule periodic challenges based on behavioral patterns
  void schedulePeriodicChallenges({
    required Function(ChallengeData) onChallenge,
    required double baseTrustScore,
    Duration interval = const Duration(minutes: 30),
  }) {
    _challengeTimer?.cancel();
    
    _challengeTimer = Timer.periodic(interval, (timer) {
      // Only challenge if trust score is declining
      if (baseTrustScore < 0.7) {
        final challenge = generateChallenge(
          1.0 - baseTrustScore,
          {'type': 'periodic', 'interval': interval.inMinutes}
        );
        onChallenge(challenge);
      }
    });
  }

  void stopPeriodicChallenges() {
    _challengeTimer?.cancel();
    _challengeTimer = null;
  }

  // Private helper methods
  ChallengeCategory _selectCategoryByRisk(double riskScore) {
    if (riskScore > 0.8) {
      // High risk: use device and behavioral patterns
      return _challengeCategories[_random.nextInt(2) + 2];
    } else if (riskScore > 0.5) {
      // Medium risk: use time and location context
      return _challengeCategories[_random.nextInt(2)];
    } else {
      // Low risk: any category
      return _challengeCategories[_random.nextInt(_challengeCategories.length)];
    }
  }

  int _getTimeoutByRisk(double riskScore) {
    if (riskScore > 0.8) return 60; // 1 minute for high risk
    if (riskScore > 0.5) return 90; // 1.5 minutes for medium risk
    return 120; // 2 minutes for low risk
  }

  int? _determineCorrectAnswer(ChallengeCategory category, int questionIndex, Map<String, dynamic> context) {
    // In a real implementation, this would use ML model predictions
    // For now, we return null to indicate user knowledge verification
    return null;
  }
}

class ChallengeCategory {
  final String id;
  final String name;
  final List<String> questions;
  final List<List<String>> options;

  ChallengeCategory({
    required this.id,
    required this.name,
    required this.questions,
    required this.options,
  });
}

class ChallengeData {
  final String id;
  final String category;
  final String question;
  final List<String> options;
  final int? correctAnswerIndex;
  final double riskScore;
  final DateTime timestamp;
  final int timeoutSeconds;

  ChallengeData({
    required this.id,
    required this.category,
    required this.question,
    required this.options,
    this.correctAnswerIndex,
    required this.riskScore,
    required this.timestamp,
    required this.timeoutSeconds,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'category': category,
      'question': question,
      'options': options,
      'correctAnswerIndex': correctAnswerIndex,
      'riskScore': riskScore,
      'timestamp': timestamp.toIso8601String(),
      'timeoutSeconds': timeoutSeconds,
    };
  }
}

class ChallengeResult {
  final String challengeId;
  final int selectedAnswer;
  final bool isCorrect;
  final Duration responseTime;
  final DateTime timestamp;

  ChallengeResult({
    required this.challengeId,
    required this.selectedAnswer,
    required this.isCorrect,
    required this.responseTime,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'challengeId': challengeId,
      'selectedAnswer': selectedAnswer,
      'isCorrect': isCorrect,
      'responseTime': responseTime.inMilliseconds,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

// 🚀 HACKATHON MODELS: Advanced Challenge System

enum ChallengeType {
  timeAwareness,
  behaviorPattern,
  contextualAwareness,
  typingDynamics,
  gestureVerification,
}

enum DifficultyLevel {
  low,
  medium,
  high,
  critical,
}

class AdaptiveChallenge {
  final String id;
  final ChallengeType type;
  final String question;
  final List<String> options;
  final int correctAnswerIndex;
  final DifficultyLevel difficulty;
  final int timeLimit; // seconds
  final String explanation;
  final double riskScore;
  final bool isContextual;
  final bool requiresTyping;
  final bool requiresGesture;
  final String? typingTarget;
  final String? gestureInstruction;

  AdaptiveChallenge({
    required this.id,
    required this.type,
    required this.question,
    required this.options,
    required this.correctAnswerIndex,
    required this.difficulty,
    required this.timeLimit,
    required this.explanation,
    required this.riskScore,
    this.isContextual = false,
    this.requiresTyping = false,
    this.requiresGesture = false,
    this.typingTarget,
    this.gestureInstruction,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString(),
      'question': question,
      'options': options,
      'correctAnswerIndex': correctAnswerIndex,
      'difficulty': difficulty.toString(),
      'timeLimit': timeLimit,
      'explanation': explanation,
      'riskScore': riskScore,
      'isContextual': isContextual,
      'requiresTyping': requiresTyping,
      'requiresGesture': requiresGesture,
      'typingTarget': typingTarget,
      'gestureInstruction': gestureInstruction,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
  }
}

class ChallengeResult {
  final String challengeId;
  final bool success;
  final double score;
  final double confidence;
  final int responseTime;
  final String feedback;
  final double behavioralConsistency;
  final double riskReduction;

  ChallengeResult({
    required this.challengeId,
    required this.success,
    required this.score,
    required this.confidence,
    required this.responseTime,
    required this.feedback,
    required this.behavioralConsistency,
    required this.riskReduction,
  });

  Map<String, dynamic> toJson() {
    return {
      'challengeId': challengeId,
      'success': success,
      'score': score,
      'confidence': confidence,
      'responseTime': responseTime,
      'feedback': feedback,
      'behavioralConsistency': behavioralConsistency,
      'riskReduction': riskReduction,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
  }
}
