import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'dart:math';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:sensors_plus/sensors_plus.dart'; // 🚀 HACKATHON: Add accelerometer
import '../firebase/firebase_service.dart';
import '../models/behavioral_data_model.dart';
import 'location_service.dart';

/// 🚀 HACKATHON ENHANCED: Multi-Modal Panic Detection System
/// Advanced panic detection using accelerometer, volume buttons, and behavioral anomalies
class PanicDetector {
  static final PanicDetector _instance = PanicDetector._internal();
  factory PanicDetector() => _instance;
  PanicDetector._internal();

  // 🚀 HACKATHON: Enhanced panic detection state
  bool _isActive = false;
  bool _isPanicModeActive = false;
  bool _isDuressMode = false;
  DateTime? _lastShakeTime;
  DateTime? _lastBehavioralAnomaly;
  int _shakeCount = 0;
  int _consecutiveAnomalies = 0;
  Timer? _shakeResetTimer;
  Timer? _volumeButtonTimer;
  Timer? _behavioralMonitorTimer;
  int _volumeButtonPressCount = 0;
  
  // 🚀 HACKATHON: Advanced detection thresholds
  static const double _shakeThreshold = 15.0; // Increased sensitivity
  static const int _shakeCountThreshold = 3;
  static const Duration _shakeWindow = Duration(seconds: 3);
  static const Duration _volumeButtonWindow = Duration(seconds: 2);
  static const int _volumeButtonThreshold = 5;
  static const int _behavioralAnomalyThreshold = 3;
  
  // 🚀 HACKATHON: Accelerometer monitoring
  StreamSubscription<AccelerometerEvent>? _accelerometerSubscription;
  List<double> _accelerometerBuffer = [];
  double _lastAcceleration = 0.0;
  
  // Emergency contacts and settings
  List<String> _emergencyContacts = [];
  String _emergencyMessage = "EMERGENCY: User may be in distress. Banking session compromised.";
  
  // Services
  final LocationService _locationService = LocationService();
  final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();
  final FirebaseService _firebaseService = FirebaseService();
  
  // 🚀 HACKATHON: Enhanced callbacks
  Function(PanicEvent)? _onPanicDetected;
  Function(PanicEvent)? _onPanicResolved;
  Function(DuressEvent)? _onDuressActivated;
  Function(BehavioralAnomaly)? _onBehavioralAnomaly;

  /// 🚀 HACKATHON: Initialize enhanced panic detection system
  void initialize({
    Function(PanicEvent)? onPanicDetected,
    Function(PanicEvent)? onPanicResolved,
    Function(DuressEvent)? onDuressActivated,
    Function(BehavioralAnomaly)? onBehavioralAnomaly,
    List<String>? emergencyContacts,
  }) {
    _onPanicDetected = onPanicDetected;
    _onPanicResolved = onPanicResolved;
    _onDuressActivated = onDuressActivated;
    _onBehavioralAnomaly = onBehavioralAnomaly;
    
    if (emergencyContacts != null) {
      _emergencyContacts = emergencyContacts;
    }
    
    _startAccelerometerMonitoring();
    _startBehavioralMonitoring();
    _isActive = true;
  }

  /// 🚀 HACKATHON: Start accelerometer-based shake detection
  void _startAccelerometerMonitoring() {
    _accelerometerSubscription = accelerometerEvents.listen((AccelerometerEvent event) {
      final double acceleration = sqrt(event.x * event.x + event.y * event.y + event.z * event.z);
      
      // Add to buffer for smoothing
      _accelerometerBuffer.add(acceleration);
      if (_accelerometerBuffer.length > 10) {
        _accelerometerBuffer.removeAt(0);
      }
      
      // Calculate smoothed acceleration
      final double smoothedAcceleration = _accelerometerBuffer.reduce((a, b) => a + b) / _accelerometerBuffer.length;
      
      // Detect significant acceleration change (shake)
      if ((smoothedAcceleration - _lastAcceleration).abs() > _shakeThreshold) {
        _handleShakeDetected();
      }
      
      _lastAcceleration = smoothedAcceleration;
    });
  }

  /// 🚀 HACKATHON: Monitor behavioral patterns for anomalies
  void _startBehavioralMonitoring() {
    _behavioralMonitorTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      _checkBehavioralAnomalies();
    });
  }

  /// 🚀 HACKATHON: Enhanced shake detection
  void _handleShakeDetected() {
    final now = DateTime.now();
    
    if (_lastShakeTime == null || now.difference(_lastShakeTime!) > _shakeWindow) {
      _shakeCount = 1;
    } else {
      _shakeCount++;
    }
    
    _lastShakeTime = now;
    
    // Reset shake count after window
    _shakeResetTimer?.cancel();
    _shakeResetTimer = Timer(_shakeWindow, () {
      _shakeCount = 0;
    });
    
    if (_shakeCount >= _shakeCountThreshold) {
      _triggerPanicEvent(PanicTrigger.shake, "Multiple shake gestures detected");
    }
  }

  /// 🚀 HACKATHON: Monitor for behavioral anomalies during banking
  void _checkBehavioralAnomalies() {
    // This would analyze current behavioral patterns against user's baseline
    // For demo, we'll simulate anomaly detection
    final random = Random();
    
    // Simulate detecting anomalies (in real implementation, this would use ML)
    if (random.nextDouble() < 0.05) { // 5% chance of anomaly detection
      _handleBehavioralAnomaly("Unusual typing pattern detected");
    }
  }

  void _handleBehavioralAnomaly(String description) {
    final now = DateTime.now();
    
    if (_lastBehavioralAnomaly == null || now.difference(_lastBehavioralAnomaly!) > Duration(minutes: 1)) {
      _consecutiveAnomalies = 1;
    } else {
      _consecutiveAnomalies++;
    }
    
    _lastBehavioralAnomaly = now;
    
    final anomaly = BehavioralAnomaly(
      timestamp: now,
      description: description,
      severity: _consecutiveAnomalies >= _behavioralAnomalyThreshold ? AnomalySeverity.high : AnomalySeverity.medium,
      consecutiveCount: _consecutiveAnomalies,
    );
    
    _onBehavioralAnomaly?.call(anomaly);
    
    if (_consecutiveAnomalies >= _behavioralAnomalyThreshold) {
      _triggerPanicEvent(PanicTrigger.behavioral, "Multiple behavioral anomalies detected");
    }
  }

  /// 🚀 HACKATHON: Enhanced volume button detection
  void handleVolumeButtonPress() {
    final now = DateTime.now();
    
    _volumeButtonTimer?.cancel();
    _volumeButtonPressCount++;
    
    _volumeButtonTimer = Timer(_volumeButtonWindow, () {
      _volumeButtonPressCount = 0;
    });
    
    if (_volumeButtonPressCount >= _volumeButtonThreshold) {
      _triggerPanicEvent(PanicTrigger.volumeButtons, "Rapid volume button presses detected");
    }
  }

  /// 🚀 HACKATHON: Trigger panic event with enhanced response
  void _triggerPanicEvent(PanicTrigger trigger, String description) async {
    if (_isPanicModeActive) return;
    
    _isPanicModeActive = true;
    
    // Create panic event
    final event = PanicEvent(
      trigger: trigger,
      timestamp: DateTime.now(),
      description: description,
      location: await _locationService.getCurrentLocation(),
      deviceInfo: await _getDeviceInfo(),
    );
    
    // Immediate response
    HapticFeedback.heavyImpact();
    SystemSound.play(SystemSoundType.alert);
    
    // Log to Firebase immediately
    await _logPanicEvent(event);
    
    // Send emergency alerts
    await _sendEmergencyAlerts(event);
    
    // Activate duress mode
    await _activateDuressMode(event);
    
    // Notify callbacks
    _onPanicDetected?.call(event);
  }

  /// 🚀 HACKATHON: Activate duress mode (fake interface)
  Future<void> _activateDuressMode(PanicEvent event) async {
    _isDuressMode = true;
    
    final duressEvent = DuressEvent(
      panicEvent: event,
      timestamp: DateTime.now(),
      fakeInterfaceActivated: true,
      realAccountLocked: true,
    );
    
    // Log duress activation
    await _firebaseService.uploadBehavioralData(
      'DURESS_MODE',
      BehavioralData()..usageTimeOfDay = 'EMERGENCY',
    );
    
    _onDuressActivated?.call(duressEvent);
  }

  /// 🚀 HACKATHON: Send emergency alerts to contacts
  Future<void> _sendEmergencyAlerts(PanicEvent event) async {
    for (final contact in _emergencyContacts) {
      try {
        // In real implementation, this would send SMS/email
        await _firebaseService.uploadBehavioralData(
          'EMERGENCY_ALERT',
          BehavioralData()..usageTimeOfDay = 'ALERT_SENT_TO_$contact',
        );
      } catch (e) {
        // Continue with other contacts if one fails
      }
    }
  }

  /// 🚀 HACKATHON: Enhanced device info collection
  Future<Map<String, dynamic>> _getDeviceInfo() async {
    final deviceInfo = <String, dynamic>{};
    
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        deviceInfo.addAll({
          'platform': 'Android',
          'model': androidInfo.model,
          'brand': androidInfo.brand,
          'version': androidInfo.version.release,
          'isPhysicalDevice': androidInfo.isPhysicalDevice,
        });
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        deviceInfo.addAll({
          'platform': 'iOS',
          'model': iosInfo.model,
          'name': iosInfo.name,
          'version': iosInfo.systemVersion,
          'isPhysicalDevice': iosInfo.isPhysicalDevice,
        });
      }
    } catch (e) {
      deviceInfo['error'] = e.toString();
    }
    
    return deviceInfo;
  }

  /// 🚀 HACKATHON: Comprehensive panic event logging
  Future<void> _logPanicEvent(PanicEvent event) async {
    try {
      await _firebaseService.uploadBehavioralData(
        'PANIC_EVENT',
        BehavioralData()
          ..usageTimeOfDay = 'EMERGENCY'
          ..addKeystroke(DateTime.now().millisecondsSinceEpoch),
      );
      
      // Also log to device storage for redundancy
      final eventData = {
        'timestamp': event.timestamp.toIso8601String(),
        'trigger': event.trigger.toString(),
        'description': event.description,
        'location': event.location?.toJson(),
        'device_info': event.deviceInfo,
        'shake_count': _shakeCount,
        'volume_button_count': _volumeButtonPressCount,
        'behavioral_anomalies': _consecutiveAnomalies,
      };
      
      // In real implementation, save to secure local storage
      print('PANIC EVENT LOGGED: $eventData');
    } catch (e) {
      // Ensure panic logging doesn't fail silently
      print('Failed to log panic event: $e');
    }
  }
    Function(PanicEvent)? onPanicResolved,
    VoidCallback? onDuressActivated,
  }) {
    _onPanicDetected = onPanicDetected;
    _onPanicResolved = onPanicResolved;
    _onDuressActivated = onDuressActivated;
    
    _setupVolumeButtonListener();
    _isActive = true;
  }

  /// Process accelerometer data for shake detection
  void processAccelerometerData(double x, double y, double z) {
    if (!_isActive) return;
    
    final acceleration = sqrt(x * x + y * y + z * z);
    
    if (acceleration > _shakeThreshold) {
      _handleShakeDetected();
    }
  }

  /// Handle shake detection
  void _handleShakeDetected() {
    final now = DateTime.now();
    
    if (_lastShakeTime == null || 
        now.difference(_lastShakeTime!) > _shakeWindow) {
      _shakeCount = 1;
    } else {
      _shakeCount++;
    }
    
    _lastShakeTime = now;
    
    // Reset shake count after window
    _shakeResetTimer?.cancel();
    _shakeResetTimer = Timer(_shakeWindow, () {
      _shakeCount = 0;
    });
    
    // Trigger panic if threshold reached
    if (_shakeCount >= _shakeCountThreshold) {
      _triggerPanic(PanicTrigger.shakeGesture);
    }
  }

  /// Setup volume button listener
  void _setupVolumeButtonListener() {
    // Note: This is a simplified version. In a real app, you'd use
    // volume_control or similar plugin to detect volume button presses
    
    // For now, we'll expose a method to manually trigger volume button detection
    // This would be called by the native Android code
  }

  /// Handle volume button press (called from native code)
  void handleVolumeButtonPress() {
    if (!_isActive) return;
    
    _volumeButtonPressCount++;
    
    _volumeButtonTimer?.cancel();
    _volumeButtonTimer = Timer(_volumeButtonWindow, () {
      _volumeButtonPressCount = 0;
    });
    
    if (_volumeButtonPressCount >= _volumeButtonThreshold) {
      _triggerPanic(PanicTrigger.volumeButton);
    }
  }

  /// Trigger panic mode
  void _triggerPanic(PanicTrigger trigger) async {
    if (_isPanicModeActive) return;

    _isPanicModeActive = true;

    // Get real location and device info
    final location = await _getLocationString();
    final deviceId = await _getDeviceId();

    final event = PanicEvent(
      trigger: trigger,
      timestamp: DateTime.now(),
      location: location,
      deviceId: deviceId,
    );

    _onPanicDetected?.call(event);

    // Send silent alert
    _sendSilentAlert(event);

    // Trigger haptic feedback
    HapticFeedback.vibrate();

    // Auto-resolve after 5 minutes if not manually resolved
    Timer(Duration(minutes: 5), () {
      if (_isPanicModeActive) {
        _resolvePanic();
      }
    });
  }

  /// Send silent alert to emergency contacts/security
  Future<void> _sendSilentAlert(PanicEvent event) async {
    try {
      // Send to Firebase for emergency response
      await _firebaseService.sendEmergencyAlert(
        'panic_alert',
        'panic_alert',
        {
          'type': 'panic_alert',
          'trigger': event.trigger.toString(),
          'timestamp': event.timestamp.toIso8601String(),
          'location': event.location,
          'deviceId': event.deviceId,
          'severity': 'high',
          'silent': true,
        },
      );
    } catch (e) {
      // Silent alert failed - continue without alerting user
    }
  }

  /// Manually trigger panic (for testing or UI button)
  void triggerManualPanic() {
    _triggerPanic(PanicTrigger.manual);
  }

  /// Resolve panic mode
  void _resolvePanic() async {
    if (!_isPanicModeActive) return;

    _isPanicModeActive = false;

    // Get real location and device info
    final location = await _getLocationString();
    final deviceId = await _getDeviceId();

    final event = PanicEvent(
      trigger: PanicTrigger.resolved,
      timestamp: DateTime.now(),
      location: location,
      deviceId: deviceId,
    );

    _onPanicResolved?.call(event);

    // Send resolution notification
    _sendResolutionNotification(event);
  }

  /// Send resolution notification
  Future<void> _sendResolutionNotification(PanicEvent event) async {
    try {
      await _firebaseService.sendEmergencyAlert(
        'panic_resolved',
        'panic_resolved',
        {
          'type': 'panic_resolved',
          'timestamp': event.timestamp.toIso8601String(),
          'deviceId': event.deviceId,
          'severity': 'info',
        },
      );
    } catch (e) {
      // Resolution notification failed - continue
    }
  }

  /// Activate duress mode (appears normal but sends alerts)
  void activateDuressMode() {
    _onDuressActivated?.call();
    
    // Send duress alert
    _sendDuressAlert();
  }

  /// Send duress alert (user appears to be acting normally but is in distress)
  Future<void> _sendDuressAlert() async {
    try {
      // Get real location and device info
      final location = await _getLocationString();
      final deviceId = await _getDeviceId();

      await _firebaseService.sendEmergencyAlert(
        'duress_alert',
        'duress_alert',
        {
          'type': 'duress_alert',
          'timestamp': DateTime.now().toIso8601String(),
          'deviceId': deviceId,
          'location': location,
          'severity': 'critical',
          'silent': true,
          'message': 'User may be under duress - appears normal but triggered alert',
        },
      );
    } catch (e) {
      // Duress alert failed - continue silently
    }
  }

  /// Check if panic mode is active
  bool get isPanicModeActive => _isPanicModeActive;

  /// Stop panic detection
  void stop() {
    _isActive = false;
    _shakeResetTimer?.cancel();
    _volumeButtonTimer?.cancel();
    _isPanicModeActive = false;
  }

  /// Get device ID for panic alerts
  Future<String> _getDeviceId() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        return 'android_${androidInfo.id}';
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        return 'ios_${iosInfo.identifierForVendor ?? 'unknown'}';
      }
      return 'unknown_device';
    } catch (e) {
      return 'device_error';
    }
  }

  /// Get location string for panic alerts
  Future<String> _getLocationString() async {
    try {
      return await _locationService.getLocationString();
    } catch (e) {
      return 'Location unavailable';
    }
  }

  /// Dispose resources
  void dispose() {
    stop();
    _onPanicDetected = null;
    _onPanicResolved = null;
    _onDuressActivated = null;
  }
}

enum PanicTrigger {
  shakeGesture,
  volumeButton,
  manual,
  resolved,
}

class PanicEvent {
  final PanicTrigger trigger;
  final DateTime timestamp;
  final String location;
  final String deviceId;

  PanicEvent({
    required this.trigger,
    required this.timestamp,
    required this.location,
    required this.deviceId,
  });

  Map<String, dynamic> toJson() {
    return {
      'trigger': trigger.toString(),
      'timestamp': timestamp.toIso8601String(),
      'location': location,
      'deviceId': deviceId,
    };
  }
}

class PanicButton extends StatefulWidget {
  final VoidCallback? onPanicTriggered;
  final bool isEmergencyMode;

  const PanicButton({
    Key? key,
    this.onPanicTriggered,
    this.isEmergencyMode = false,
  }) : super(key: key);

  @override
  State<PanicButton> createState() => _PanicButtonState();
}

class _PanicButtonState extends State<PanicButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<Color?> _colorAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _colorAnimation = ColorTween(
      begin: Colors.red,
      end: Colors.red[800],
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    if (widget.isEmergencyMode) {
      _animationController.repeat(reverse: true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: FloatingActionButton(
            onPressed: () {
              HapticFeedback.heavyImpact();
              _showPanicConfirmation();
            },
            backgroundColor: _colorAnimation.value,
            child: Icon(
              widget.isEmergencyMode ? Icons.warning : Icons.emergency,
              color: Colors.white,
              size: 28,
            ),
          ),
        );
      },
    );
  }

  void _showPanicConfirmation() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.warning, color: Colors.red),
            SizedBox(width: 8),
            Text('Emergency Alert'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'This will send a silent emergency alert to your contacts and security team.',
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 16),
            Text(
              'Are you sure you want to proceed?',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              PanicDetector().triggerManualPanic();
              widget.onPanicTriggered?.call();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text('Send Alert'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
}

// 🚀 HACKATHON: Enhanced Models for Advanced Panic Detection

enum PanicTrigger {
  shake,
  volumeButtons,
  behavioral,
  manual,
  resolved,
}

enum AnomalySeverity {
  low,
  medium,
  high,
  critical,
}

class PanicEvent {
  final PanicTrigger trigger;
  final DateTime timestamp;
  final String description;
  final Map<String, dynamic>? location;
  final Map<String, dynamic> deviceInfo;
  final int? shakeCount;
  final int? volumeButtonCount;
  final int? behavioralAnomalies;

  PanicEvent({
    required this.trigger,
    required this.timestamp,
    required this.description,
    this.location,
    required this.deviceInfo,
    this.shakeCount,
    this.volumeButtonCount,
    this.behavioralAnomalies,
  });

  Map<String, dynamic> toJson() {
    return {
      'trigger': trigger.toString(),
      'timestamp': timestamp.toIso8601String(),
      'description': description,
      'location': location,
      'device_info': deviceInfo,
      'shake_count': shakeCount,
      'volume_button_count': volumeButtonCount,
      'behavioral_anomalies': behavioralAnomalies,
    };
  }
}

class DuressEvent {
  final PanicEvent panicEvent;
  final DateTime timestamp;
  final bool fakeInterfaceActivated;
  final bool realAccountLocked;
  final List<String> alertsSent;

  DuressEvent({
    required this.panicEvent,
    required this.timestamp,
    required this.fakeInterfaceActivated,
    required this.realAccountLocked,
    this.alertsSent = const [],
  });

  Map<String, dynamic> toJson() {
    return {
      'panic_event': panicEvent.toJson(),
      'timestamp': timestamp.toIso8601String(),
      'fake_interface_activated': fakeInterfaceActivated,
      'real_account_locked': realAccountLocked,
      'alerts_sent': alertsSent,
    };
  }
}

class BehavioralAnomaly {
  final DateTime timestamp;
  final String description;
  final AnomalySeverity severity;
  final int consecutiveCount;
  final Map<String, dynamic>? additionalData;

  BehavioralAnomaly({
    required this.timestamp,
    required this.description,
    required this.severity,
    required this.consecutiveCount,
    this.additionalData,
  });

  Map<String, dynamic> toJson() {
    return {
      'timestamp': timestamp.toIso8601String(),
      'description': description,
      'severity': severity.toString(),
      'consecutive_count': consecutiveCount,
      'additional_data': additionalData,
    };
  }
}
