import 'package:flutter/material.dart';
import 'dart:math';
import 'dart:async';

class StandaloneDemo extends StatefulWidget {
  const StandaloneDemo({super.key});

  @override
  State<StandaloneDemo> createState() => _StandaloneDemoState();
}

class _StandaloneDemoState extends State<StandaloneDemo>
    with TickerProviderStateMixin {
  late AnimationController _typingController;
  late AnimationController _confidenceController;
  late AnimationController _scanController;
  
  double _currentConfidence = 0.0;
  String _typedText = '';
  String _targetText = 'Transfer ₹50,000 to Savings Account';
  bool _isAuthenticated = false;
  bool _showDemo = false;
  int _currentStep = 0;
  Timer? _demoTimer;

  final List<String> _features = [
    'Keystroke Dynamics',
    'Typing Speed',
    'Pressure Patterns',
    'Behavioral Biometrics',
    'ML Authentication'
  ];

  @override
  void initState() {
    super.initState();
    _typingController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _confidenceController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _scanController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _typingController.dispose();
    _confidenceController.dispose();
    _scanController.dispose();
    _demoTimer?.cancel();
    super.dispose();
  }

  void _startDemo() {
    setState(() {
      _showDemo = true;
      _currentStep = 0;
    });
    _runDemoStep();
  }

  void _runDemoStep() {
    _demoTimer?.cancel();
    
    switch (_currentStep) {
      case 0:
        // Step 1: Show typing simulation
        _simulateTyping();
        break;
      case 1:
        // Step 2: Analyze behavioral patterns
        _analyzeBehavior();
        break;
      case 2:
        // Step 3: Show authentication success
        _showAuthentication();
        break;
    }
  }

  void _simulateTyping() {
    _typedText = '';
    _currentConfidence = 0.0;
    
    Timer.periodic(const Duration(milliseconds: 150), (timer) {
      if (_typedText.length < _targetText.length) {
        setState(() {
          _typedText += _targetText[_typedText.length];
          _currentConfidence = (_typedText.length / _targetText.length) * 95;
        });
        _typingController.forward().then((_) => _typingController.reverse());
      } else {
        timer.cancel();
        _demoTimer = Timer(const Duration(seconds: 1), () {
          setState(() => _currentStep = 1);
          _runDemoStep();
        });
      }
    });
  }

  void _analyzeBehavior() {
    _confidenceController.forward();
    _demoTimer = Timer(const Duration(seconds: 3), () {
      setState(() => _currentStep = 2);
      _runDemoStep();
    });
  }

  void _showAuthentication() {
    setState(() {
      _isAuthenticated = true;
      _currentConfidence = 98.5;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0D1B2A),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: !_showDemo ? _buildWelcomeScreen() : _buildDemoScreen(),
        ),
      ),
    );
  }

  Widget _buildWelcomeScreen() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Animated logo
        AnimatedBuilder(
          animation: _scanController,
          builder: (context, child) {
            return Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: RadialGradient(
                  colors: [
                    Colors.blue.withOpacity(0.8),
                    Colors.purple.withOpacity(0.6),
                    Colors.transparent,
                  ],
                  stops: [0.0, _scanController.value, 1.0],
                ),
              ),
              child: const Icon(
                Icons.security,
                size: 60,
                color: Colors.white,
              ),
            );
          },
        ),
        const SizedBox(height: 32),
        
        const Text(
          'Canara Bank Hackathon',
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            letterSpacing: 1.2,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        
        const Text(
          'Behavioral Authentication Demo',
          style: TextStyle(
            fontSize: 20,
            color: Colors.grey,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 48),
        
        // Features showcase
        Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.05),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.blue.withOpacity(0.3)),
          ),
          child: Column(
            children: [
              const Text(
                '🚀 Revolutionary Features',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 16),
              ..._features.map((feature) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    const Icon(Icons.check_circle, color: Colors.green, size: 20),
                    const SizedBox(width: 12),
                    Text(
                      feature,
                      style: const TextStyle(color: Colors.white70, fontSize: 16),
                    ),
                  ],
                ),
              )),
            ],
          ),
        ),
        const SizedBox(height: 48),
        
        // Start demo button
        SizedBox(
          width: double.infinity,
          height: 56,
          child: ElevatedButton(
            onPressed: _startDemo,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              elevation: 8,
            ),
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.play_arrow, size: 24),
                SizedBox(width: 8),
                Text(
                  'Start Live Demo',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDemoScreen() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Row(
          children: [
            IconButton(
              onPressed: () {
                setState(() {
                  _showDemo = false;
                  _currentStep = 0;
                  _isAuthenticated = false;
                });
                _demoTimer?.cancel();
              },
              icon: const Icon(Icons.arrow_back, color: Colors.white),
            ),
            const Expanded(
              child: Text(
                'Live Authentication Demo',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(width: 48),
          ],
        ),
        const SizedBox(height: 32),
        
        // Status indicator
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: _isAuthenticated ? Colors.green.withOpacity(0.1) : Colors.blue.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: _isAuthenticated ? Colors.green : Colors.blue,
            ),
          ),
          child: Row(
            children: [
              Icon(
                _isAuthenticated ? Icons.verified_user : Icons.security,
                color: _isAuthenticated ? Colors.green : Colors.blue,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                _isAuthenticated ? 'Authentication Successful' : 'Analyzing Behavior...',
                style: TextStyle(
                  color: _isAuthenticated ? Colors.green : Colors.blue,
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 32),
        
        // Typing simulation
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.05),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.withOpacity(0.3)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Transaction Input:',
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              AnimatedBuilder(
                animation: _typingController,
                builder: (context, child) {
                  return Text(
                    '$_typedText${_typingController.isAnimating ? '|' : ''}',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      height: 1.5,
                    ),
                  );
                },
              ),
            ],
          ),
        ),
        const SizedBox(height: 32),
        
        // Confidence meter
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Authentication Confidence',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '${_currentConfidence.toStringAsFixed(1)}%',
                  style: TextStyle(
                    color: _currentConfidence > 90 ? Colors.green : Colors.orange,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              height: 8,
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.3),
                borderRadius: BorderRadius.circular(4),
              ),
              child: FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: _currentConfidence / 100,
                child: Container(
                  decoration: BoxDecoration(
                    color: _currentConfidence > 90 ? Colors.green : Colors.orange,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 32),
        
        // Behavioral analysis
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.05),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.withOpacity(0.3)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Behavioral Pattern Analysis',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 16),
              ...List.generate(3, (index) {
                final values = [
                  ('Keystroke Rhythm', _currentConfidence > 30 ? 96.2 : 0.0),
                  ('Typing Speed', _currentConfidence > 60 ? 94.8 : 0.0),
                  ('Pressure Pattern', _currentConfidence > 90 ? 97.1 : 0.0),
                ];
                
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        values[index].$1,
                        style: const TextStyle(color: Colors.white70),
                      ),
                      Text(
                        values[index].$2 > 0 ? '${values[index].$2}%' : 'Analyzing...',
                        style: TextStyle(
                          color: values[index].$2 > 90 ? Colors.green : Colors.orange,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                );
              }),
            ],
          ),
        ),
        
        const Spacer(),
        
        // Action button
        if (_isAuthenticated)
          SizedBox(
            width: double.infinity,
            height: 56,
            child: ElevatedButton(
              onPressed: () {
                // Show success message
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('🎉 Transaction Authorized via Behavioral Authentication!'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child: const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.check_circle, size: 24),
                  SizedBox(width: 8),
                  Text(
                    'Complete Transaction',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }
}
