plugins {
    id("com.android.application")
    id("kotlin-android")
    id("dev.flutter.flutter-gradle-plugin")
    id("com.google.gms.google-services")
}

android {
    namespace = "com.sentinelauth.banking"
    compileSdk = 35 // Updated for compatibility
    ndkVersion = "27.0.********" // ✅ Explicit NDK version required by Firebase plugins

    defaultConfig {
        applicationId = "com.sentinelauth.banking" // ✅ MUST match google-services.json
        minSdk = 24 // Higher minimum SDK for banking security
        targetSdk = 35 // Updated for compatibility
        versionCode = 1
        versionName = "1.0"
        
        // Security configurations for banking
        multiDexEnabled = true
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            // Note: For production, you should use proper release signing configuration
            // signingConfig = signingConfigs.getByName("release")
            signingConfig = signingConfigs.getByName("debug") // Temporary for development
            isMinifyEnabled = true
            isShrinkResources = true
            isDebuggable = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
        debug {
            isDebuggable = true
            // applicationIdSuffix = ".debug" // Removed to match google-services.json
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }
    
    // Security configurations
    packagingOptions {
        pickFirst("**/libc++_shared.so")
        pickFirst("**/libjsc.so")
    }
}

flutter {
    source = "../.."
}
