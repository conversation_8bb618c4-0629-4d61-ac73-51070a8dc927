import 'package:flutter/material.dart';
import '../screens/splash_screen.dart';
import '../screens/onboarding_screen.dart';
import '../screens/register_screen.dart';
import '../screens/secure_login_screen.dart';
import '../screens/behavioral_auth_screen.dart';
import '../screens/dashboard_screen.dart';
import '../screens/home_screen.dart';
import '../screens/activity_screen.dart';
import '../screens/payment_success_screen.dart';
import '../screens/privacy_dashboard_screen.dart';
import '../screens/behavioral_data_screen.dart';
import '../screens/session_export_screen.dart';
import '../screens/admin_trust_score_screen.dart';
import '../screens/profile_screen.dart';
import '../models/behavioral_data_model.dart';

class AppRoutes {
  // Authentication Routes
  static const String firstScreen = '/';
  static const String splash = '/splash';
  static const String onboarding = '/onboarding';
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  static const String resetPassword = '/reset-password';
  static const String phoneVerification = '/phone-verification';
  static const String emailVerification = '/email-verification';
  
  // Security Routes
  static const String pinSetup = '/pin-setup';
  static const String pinVerification = '/pin-verification';
  static const String biometricSetup = '/biometric-setup';
  static const String behavioralAuth = '/behavioral-auth';
  static const String securityQuestions = '/security-questions';
  static const String deviceRegistration = '/device-registration';
  
  // Main App Routes
  static const String dashboard = '/dashboard';
  static const String home = '/home';
  static const String activity = '/activity';
  static const String profile = '/profile';
  static const String settings = '/settings';
  
  // Banking Features
  static const String transfer = '/transfer';
  static const String payBills = '/pay-bills';
  static const String cardManagement = '/card-management';
  static const String accountDetails = '/account-details';
  static const String transactionHistory = '/transaction-history';
  static const String paymentSuccess = '/payment-success';
  static const String paymentFailed = '/payment-failed';
  
  // Security & Privacy
  static const String privacyDashboard = '/privacy-dashboard';
  static const String behavioralData = '/behavioral-data';
  static const String sessionExport = '/session-export';
  static const String adminTrustScore = '/admin-trust-score';
  static const String securitySettings = '/security-settings';
  static const String deviceManagement = '/device-management';
  static const String sessionManagement = '/session-management';
  
  // 🚀 HACKATHON: Demo & ML Features
  static const String hackathonDemo = '/hackathon-demo';
  static const String mlDashboard = '/ml-dashboard';
  
  // Support & Help
  static const String helpCenter = '/help-center';
  static const String contactSupport = '/contact-support';
  static const String faq = '/faq';
  
  // Error & Maintenance
  static const String error = '/error';
  static const String maintenance = '/maintenance';
  static const String networkError = '/network-error';
}

class AppRouter {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case AppRoutes.firstScreen:
        return MaterialPageRoute(builder: (_) => const SplashScreen());

      case AppRoutes.splash:
        return MaterialPageRoute(builder: (_) => const SplashScreen());

      case AppRoutes.onboarding:
        return MaterialPageRoute(builder: (_) => const OnboardingScreen());

      case AppRoutes.login:
        return MaterialPageRoute(builder: (_) => const SecureLoginScreen());

      case AppRoutes.register:
        return MaterialPageRoute(builder: (_) => const RegisterScreen());

      case AppRoutes.behavioralAuth:
        return MaterialPageRoute(builder: (_) => const BehavioralAuthScreen());

      case AppRoutes.dashboard:
        return MaterialPageRoute(builder: (_) => const DashboardScreen());

      case AppRoutes.home:
        final args = settings.arguments as Map<String, dynamic>?;
        final behavioralData = args?['behavioralData'] as BehavioralData?;
        final userId = args?['userId'] as String?;

        if (behavioralData != null && userId != null) {
          return MaterialPageRoute(
            builder: (_) => HomeScreen(
              behavioralData: behavioralData,
              userId: userId,
            ),
          );
        }
        return MaterialPageRoute(builder: (_) => const DashboardScreen());
      
      case AppRoutes.activity:
        return MaterialPageRoute(builder: (_) => const ActivityScreen());
      
      case AppRoutes.paymentSuccess:
        return MaterialPageRoute(builder: (_) => const PaymentSuccessScreen());
      
      case AppRoutes.privacyDashboard:
        return MaterialPageRoute(builder: (_) => const PrivacyDashboardScreen());
      
      case AppRoutes.behavioralData:
        final args = settings.arguments as Map<String, dynamic>?;
        final data = args?['data'] as BehavioralData?;
        return MaterialPageRoute(
          builder: (_) => BehavioralDataScreen(data: data),
        );

      case AppRoutes.sessionExport:
        final args = settings.arguments as Map<String, dynamic>?;
        final userId = args?['userId'] as String? ?? 'default_user';
        final currentSession = args?['currentSession'] as BehavioralData?;
        return MaterialPageRoute(
          builder: (_) => SessionExportScreen(
            userId: userId,
            currentSession: currentSession,
          ),
        );

      case AppRoutes.adminTrustScore:
        final args = settings.arguments as Map<String, dynamic>?;
        final adminId = args?['adminId'] as String? ?? 'default_admin';
        return MaterialPageRoute(
          builder: (_) => AdminTrustScoreScreen(adminId: adminId),
        );

      case AppRoutes.profile:
        return MaterialPageRoute(builder: (_) => const ProfileScreen());

      default:
        return MaterialPageRoute(
          builder: (_) => Scaffold(
            appBar: AppBar(title: const Text('Page Not Found')),
            body: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: Colors.red),
                  SizedBox(height: 16),
                  Text(
                    'Page Not Found',
                    style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text('The requested page could not be found.'),
                ],
              ),
            ),
          ),
        );
    }
  }
}

// Placeholder screens (we'll implement these)

class PinSetupScreen extends StatelessWidget {
  const PinSetupScreen({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('PIN Setup')));
}

class PinVerificationScreen extends StatelessWidget {
  const PinVerificationScreen({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('PIN Verification')));
}

class BiometricSetupScreen extends StatelessWidget {
  const BiometricSetupScreen({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Biometric Setup')));
}

// BehavioralAuthScreen is imported from ../screens/behavioral_auth_screen.dart

class DeviceRegistrationScreen extends StatelessWidget {
  const DeviceRegistrationScreen({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Device Registration')));
}

class SecuritySettingsScreen extends StatelessWidget {
  const SecuritySettingsScreen({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Security Settings')));
}

class HelpCenterScreen extends StatelessWidget {
  const HelpCenterScreen({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Help Center')));
}

class ContactSupportScreen extends StatelessWidget {
  const ContactSupportScreen({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Contact Support')));
}

class BehavioralDataPlaceholderScreen extends StatelessWidget {
  const BehavioralDataPlaceholderScreen({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Behavioral Data'),
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      backgroundColor: const Color(0xFF0D1B2A),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.psychology, size: 80, color: Colors.white54),
            SizedBox(height: 16),
            Text(
              'No Behavioral Data Available',
              style: TextStyle(color: Colors.white, fontSize: 18),
            ),
            SizedBox(height: 8),
            Text(
              'Data will appear after authentication sessions',
              style: TextStyle(color: Colors.white54, fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}

class ErrorScreen extends StatelessWidget {
  final String title;
  final String message;
  
  const ErrorScreen({
    Key? key,
    required this.title,
    required this.message,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(title),
            Text(message),
          ],
        ),
      ),
    );
  }
}
