import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import '../services/real_behavioral_engine.dart';

class FunctionalBankingApp extends StatefulWidget {
  const FunctionalBankingApp({super.key});

  @override
  State<FunctionalBankingApp> createState() => _FunctionalBankingAppState();
}

class _FunctionalBankingAppState extends State<FunctionalBankingApp> {
  final RealBehavioralEngine _engine = RealBehavioralEngine();
  final TextEditingController _textController = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  String _selectedUser = 'demo_user';
  bool _isAnalyzing = false;
  AuthenticationResult? _lastResult;
  SessionStats? _sessionStats;
  UserBehavioralProfile? _currentProfile;
  
  List<String> _keystrokeLog = [];
  DateTime? _sessionStart;

  @override
  void initState() {
    super.initState();
    _engine.initialize();
    _loadUserProfile();
    _sessionStart = DateTime.now();
    
    // Listen to text changes for real-time analysis
    _textController.addListener(_onTextChanged);
    
    // Auto-focus for immediate typing
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _loadUserProfile() {
    _currentProfile = _engine.getUserProfile(_selectedUser);
    setState(() {});
  }

  void _onTextChanged() {
    final currentTime = DateTime.now().millisecondsSinceEpoch.toDouble();
    final text = _textController.text;
    
    if (text.isNotEmpty) {
      final lastChar = text.substring(text.length - 1);
      
      // Process keystroke with real-time data
      _engine.processKeystroke(
        lastChar, 
        currentTime,
        0.7 + (DateTime.now().millisecond % 30) / 100, // Simulate pressure variance
      );
      
      // Update keystroke log
      _keystrokeLog.add('${lastChar} (${DateTime.now().millisecond}ms)');
      if (_keystrokeLog.length > 10) {
        _keystrokeLog.removeAt(0);
      }
      
      // Update session stats
      _sessionStats = _engine.getSessionStats();
      setState(() {});
    }
  }

  Future<void> _authenticateUser() async {
    if (_textController.text.isEmpty) {
      _showError('Please type something to analyze behavioral patterns');
      return;
    }

    setState(() {
      _isAnalyzing = true;
      _lastResult = null;
    });

    try {
      // Real authentication using behavioral engine
      final result = await _engine.authenticateUser(_selectedUser, _textController.text);
      
      setState(() {
        _lastResult = result;
        _isAnalyzing = false;
      });

      // Show result
      if (result.success) {
        _showSuccess('Authentication Successful!\nConfidence: ${(result.confidence * 100).toStringAsFixed(1)}%');
      } else {
        _showError('Authentication Failed\nReason: ${result.reason}');
      }
      
    } catch (e) {
      setState(() {
        _isAnalyzing = false;
      });
      _showError('Authentication error: $e');
    }
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _switchUser(String userId) {
    setState(() {
      _selectedUser = userId;
      _textController.clear();
      _keystrokeLog.clear();
      _lastResult = null;
    });
    _engine.clearSession();
    _loadUserProfile();
    _focusNode.requestFocus();
  }

  void _clearSession() {
    setState(() {
      _textController.clear();
      _keystrokeLog.clear();
      _lastResult = null;
      _sessionStats = null;
    });
    _engine.clearSession();
    _sessionStart = DateTime.now();
    _focusNode.requestFocus();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0D1B2A),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              _buildHeader(),
              const SizedBox(height: 24),
              
              // User Selection
              _buildUserSelection(),
              const SizedBox(height: 24),
              
              // User Profile Info
              if (_currentProfile != null) _buildProfileInfo(),
              const SizedBox(height: 24),
              
              // Transaction Input
              _buildTransactionInput(),
              const SizedBox(height: 24),
              
              // Real-time Analysis
              _buildRealTimeAnalysis(),
              const SizedBox(height: 24),
              
              // Authentication Button
              _buildAuthButton(),
              const SizedBox(height: 24),
              
              // Results
              if (_lastResult != null) _buildResults(),
              const SizedBox(height: 16),
              
              // Session Stats
              if (_sessionStats != null) _buildSessionStats(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue.shade800, Colors.blue.shade600],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.security, color: Colors.white, size: 32),
              SizedBox(width: 12),
              Text(
                'Canara Bank',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          const Text(
            'Behavioral Authentication System',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.green,
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Text(
              '🔴 LIVE SYSTEM - Real Processing',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserSelection() {
    final availableUsers = _engine.getAvailableUsers();
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Select User Profile:',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            children: availableUsers.map((userId) {
              final isSelected = userId == _selectedUser;
              return ChoiceChip(
                label: Text(userId),
                selected: isSelected,
                onSelected: (_) => _switchUser(userId),
                selectedColor: Colors.blue,
                backgroundColor: Colors.grey.shade800,
                labelStyle: TextStyle(
                  color: isSelected ? Colors.white : Colors.grey.shade300,
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileInfo() {
    final profile = _currentProfile!;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'User Profile: ${profile.userId}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Sessions',
                  profile.sessionCount.toString(),
                  Icons.history,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildStatCard(
                  'Confidence',
                  '${(profile.confidence * 100).toInt()}%',
                  Icons.verified,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildStatCard(
                  'Speed',
                  '${profile.keystrokeProfile.typingSpeed.toInt()} WPM',
                  Icons.speed,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: Colors.blue, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            label,
            style: const TextStyle(
              color: Colors.grey,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Transaction Authorization',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            'Type your transaction details (your typing patterns are being analyzed in real-time):',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 12),
          TextField(
            controller: _textController,
            focusNode: _focusNode,
            maxLines: 3,
            decoration: InputDecoration(
              hintText: 'Transfer ₹50,000 to Account: **********\nIFSC: CNRB0001234\nPurpose: Salary transfer',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.blue),
              ),
            ),
            onSubmitted: (_) => _authenticateUser(),
          ),
        ],
      ),
    );
  }

  Widget _buildRealTimeAnalysis() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.analytics, color: Colors.orange, size: 20),
              SizedBox(width: 8),
              Text(
                'Real-Time Behavioral Analysis',
                style: TextStyle(
                  color: Colors.orange,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          if (_keystrokeLog.isNotEmpty) ...[
            const Text(
              'Recent Keystrokes:',
              style: TextStyle(color: Colors.white, fontSize: 12),
            ),
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.3),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                _keystrokeLog.join(' → '),
                style: const TextStyle(
                  color: Colors.green,
                  fontSize: 10,
                  fontFamily: 'monospace',
                ),
              ),
            ),
          ] else ...[
            const Text(
              'Start typing to see real-time analysis...',
              style: TextStyle(color: Colors.grey, fontSize: 12),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAuthButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: _isAnalyzing ? null : _authenticateUser,
        style: ElevatedButton.styleFrom(
          backgroundColor: _isAnalyzing ? Colors.grey : Colors.blue,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 8,
        ),
        child: _isAnalyzing
            ? const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  SizedBox(width: 12),
                  Text('Processing Behavioral Data...'),
                ],
              )
            : const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.fingerprint, size: 24),
                  SizedBox(width: 8),
                  Text(
                    'Authenticate Transaction',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildResults() {
    final result = _lastResult!;
    final isSuccess = result.success;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isSuccess ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSuccess ? Colors.green : Colors.red,
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isSuccess ? Icons.check_circle : Icons.error,
                color: isSuccess ? Colors.green : Colors.red,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                isSuccess ? 'Authentication Successful' : 'Authentication Failed',
                style: TextStyle(
                  color: isSuccess ? Colors.green : Colors.red,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Confidence: ${(result.confidence * 100).toStringAsFixed(1)}%',
            style: const TextStyle(color: Colors.white, fontSize: 14),
          ),
          Text(
            'Reason: ${result.reason}',
            style: const TextStyle(color: Colors.white70, fontSize: 12),
          ),
          if (result.dwellTimeScore != null) ...[
            const SizedBox(height: 8),
            Text(
              'Behavioral Score: ${(result.dwellTimeScore! * 100).toStringAsFixed(1)}%',
              style: const TextStyle(color: Colors.white70, fontSize: 12),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSessionStats() {
    final stats = _sessionStats!;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Session Statistics',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              TextButton(
                onPressed: _clearSession,
                child: const Text('Clear Session'),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildSessionStat('Keystrokes', stats.keystrokeCount.toString()),
              ),
              Expanded(
                child: _buildSessionStat('Speed', '${stats.avgTypingSpeed.toStringAsFixed(1)} WPM'),
              ),
              Expanded(
                child: _buildSessionStat('Duration', '${stats.sessionDuration}ms'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSessionStat(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            color: Colors.blue,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            color: Colors.grey,
            fontSize: 12,
          ),
        ),
      ],
    );
  }
}
