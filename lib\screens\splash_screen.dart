import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../widgets/app_widgets.dart';
import '../services/auth_service.dart';
import '../services/security_service.dart';
import '../routes/app_routes.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _textController;
  late Animation<double> _logoAnimation;
  late Animation<double> _textAnimation;
  late Animation<Offset> _slideAnimation;

  String _statusText = 'Initializing security...';
  bool _isError = false;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _initializeApp();
  }

  void _setupAnimations() {
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _textController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _logoAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    _textAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeOutCubic,
    ));

    _logoController.forward();
    Future.delayed(const Duration(milliseconds: 500), () {
      _textController.forward();
    });
  }

  Future<void> _initializeApp() async {
    try {
      // Step 1: Initialize security services
      setState(() => _statusText = 'Initializing security protocols...');
      await Future.delayed(const Duration(milliseconds: 800));
      await SecurityService.initialize();

      // Step 2: Check device security
      setState(() => _statusText = 'Verifying device security...');
      await Future.delayed(const Duration(milliseconds: 600));
      final isDeviceSecure = await SecurityService.checkDeviceSecurity();
      
      if (!isDeviceSecure) {
        setState(() {
          _statusText = 'Device security check failed';
          _isError = true;
        });
        await Future.delayed(const Duration(seconds: 2));
        _navigateToSecurityWarning();
        return;
      }

      // Step 3: Initialize biometric services
      setState(() => _statusText = 'Setting up biometric authentication...');
      await Future.delayed(const Duration(milliseconds: 600));
      await SecurityService.initializeBiometrics();

      // Step 4: Check authentication state
      setState(() => _statusText = 'Checking authentication state...');
      await Future.delayed(const Duration(milliseconds: 600));
      final authState = await AuthService.checkAuthenticationState();

      // Step 5: Initialize behavioral authentication
      setState(() => _statusText = 'Loading behavioral patterns...');
      await Future.delayed(const Duration(milliseconds: 600));
      await SecurityService.initializeBehavioralAuth();

      // Step 6: Navigate based on auth state
      setState(() => _statusText = 'Loading your secure banking experience...');
      await Future.delayed(const Duration(milliseconds: 800));
      
      _navigateBasedOnAuthState(authState);

    } catch (e) {
      setState(() {
        _statusText = 'Security initialization failed';
        _isError = true;
      });
      await Future.delayed(const Duration(seconds: 2));
      _navigateToError();
    }
  }

  void _navigateBasedOnAuthState(AuthState authState) {
    switch (authState) {
      case AuthState.firstLaunch:
        Navigator.pushReplacementNamed(context, AppRoutes.onboarding);
        break;
      case AuthState.loggedOut:
        Navigator.pushReplacementNamed(context, AppRoutes.login);
        break;
      case AuthState.requiresReauth:
        Navigator.pushReplacementNamed(context, AppRoutes.behavioralAuth);
        break;
      case AuthState.requiresPinVerification:
        Navigator.pushReplacementNamed(context, AppRoutes.pinVerification);
        break;
      case AuthState.authenticated:
        Navigator.pushReplacementNamed(context, AppRoutes.dashboard);
        break;
    }
  }

  void _navigateToSecurityWarning() {
    // Navigate to security warning screen
    Navigator.pushReplacementNamed(context, AppRoutes.error);
  }

  void _navigateToError() {
    Navigator.pushReplacementNamed(context, AppRoutes.error);
  }

  @override
  void dispose() {
    _logoController.dispose();
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.background,
              AppColors.surface,
              AppColors.background,
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              Expanded(
                flex: 3,
                child: Center(
                  child: AnimatedBuilder(
                    animation: _logoAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _logoAnimation.value,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // Banking Logo with Hand and Money
                            Container(
                              width: 120,
                              height: 120,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                gradient: LinearGradient(
                                  colors: [
                                    const Color(0xFF4285F4),
                                    const Color(0xFF34A853),
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: const Color(0xFF4285F4).withOpacity(0.3),
                                    blurRadius: 20,
                                    spreadRadius: 5,
                                  ),
                                ],
                              ),
                              child: Stack(
                                alignment: Alignment.center,
                                children: [
                                  const Icon(
                                    Icons.account_balance_wallet,
                                    size: 40,
                                    color: Colors.white,
                                  ),
                                  Positioned(
                                    top: 25,
                                    right: 25,
                                    child: Container(
                                      padding: const EdgeInsets.all(4),
                                      decoration: const BoxDecoration(
                                        color: Color(0xFFFF6B35),
                                        shape: BoxShape.circle,
                                      ),
                                      child: const Icon(
                                        Icons.security,
                                        size: 16,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 24),
                            
                            // App Name
                            const Text(
                              'Trust Chain Banking',
                              style: TextStyle(
                                fontSize: 32,
                                fontWeight: FontWeight.w800,
                                letterSpacing: 1.2,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(height: 8),

                            // Tagline
                            Text(
                              'Secure Banking with Behavioral Authentication',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.white.withOpacity(0.8),
                                letterSpacing: 0.5,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ),
              
              Expanded(
                flex: 1,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: FadeTransition(
                    opacity: _textAnimation,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Security indicators
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            _buildSecurityBadge(Icons.security, 'Encrypted'),
                            const SizedBox(width: 24),
                            _buildSecurityBadge(Icons.fingerprint, 'Biometric'),
                            const SizedBox(width: 24),
                            _buildSecurityBadge(Icons.shield, 'Protected'),
                          ],
                        ),
                        const SizedBox(height: 32),
                        
                        // Loading indicator
                        SizedBox(
                          width: 200,
                          child: LinearProgressIndicator(
                            backgroundColor: AppColors.surfaceVariant,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              _isError ? AppColors.error : AppColors.primary,
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        
                        // Status text
                        Text(
                          _statusText,
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: _isError ? AppColors.error : AppColors.textSecondary,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              
              // Footer
              Padding(
                padding: const EdgeInsets.all(24),
                child: Text(
                  'Protected by advanced security protocols',
                  style: AppTextStyles.caption.copyWith(
                    color: AppColors.textTertiary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSecurityBadge(IconData icon, String label) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppColors.surfaceVariant,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            color: AppColors.primary,
            size: 20,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: AppTextStyles.caption.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }
}
