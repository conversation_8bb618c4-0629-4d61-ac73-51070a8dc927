import 'package:flutter/material.dart';
import '../screens/functional_banking_app.dart';

class AppRoutes {
  static const String home = '/';
  static const String functionalApp = '/functional';

  static Map<String, WidgetBuilder> routes = {
    home: (context) => const FunctionalBankingApp(),
    functionalApp: (context) => const FunctionalBankingApp(),
  };

  static Route<dynamic>? onGenerateRoute(RouteSettings settings) {
    final builder = routes[settings.name];
    if (builder != null) {
      return MaterialPageRoute(
        builder: builder,
        settings: settings,
      );
    }
    
    // Default route to functional app
    return MaterialPageRoute(
      builder: (context) => const FunctionalBankingApp(),
    );
  }
}
