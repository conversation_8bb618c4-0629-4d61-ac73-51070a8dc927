import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/behavioral_data_model.dart';

class DataExporter {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  Future<void> saveJsonToFile(Map<String, dynamic> data, String filename) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/$filename.json');
    await file.writeAsString(jsonEncode(data));
  }

  Future<void> saveCsvToFile(Map<String, dynamic> data, String filename) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/$filename.csv');

    final csvBuffer = StringBuffer();
    data.forEach((key, value) {
      csvBuffer.writeln('$key,${value.toString()}');
    });

    await file.writeAsString(csvBuffer.toString());
  }

  /// Export all sessions for a specific user
  Future<void> exportUserSessions(String userId, {String format = 'json'}) async {
    try {
      final sessions = await _firestore
          .collection('users')
          .doc(userId)
          .collection('sessions')
          .orderBy('timestamp', descending: true)
          .get();

      final List<Map<String, dynamic>> sessionData = [];
      
      for (var doc in sessions.docs) {
        final data = doc.data();
        data['sessionId'] = doc.id;
        sessionData.add(data);
      }

      final exportData = {
        'userId': userId,
        'exportTimestamp': DateTime.now().toIso8601String(),
        'totalSessions': sessionData.length,
        'sessions': sessionData,
      };

      final filename = 'user_sessions_${userId}_${DateTime.now().millisecondsSinceEpoch}';
      
      if (format.toLowerCase() == 'json') {
        await saveJsonToFile(exportData, filename);
      } else if (format.toLowerCase() == 'csv') {
        await _saveSessionsCsv(exportData, filename);
      }
    } catch (e) {
      rethrow;
    }
  }

  /// Export sessions within a date range
  Future<void> exportSessionsByDateRange(
    String userId,
    DateTime startDate,
    DateTime endDate, {
    String format = 'json',
  }) async {
    try {
      final sessions = await _firestore
          .collection('users')
          .doc(userId)
          .collection('sessions')
          .where('timestamp', isGreaterThanOrEqualTo: startDate.toIso8601String())
          .where('timestamp', isLessThanOrEqualTo: endDate.toIso8601String())
          .orderBy('timestamp', descending: true)
          .get();

      final List<Map<String, dynamic>> sessionData = [];
      
      for (var doc in sessions.docs) {
        final data = doc.data();
        data['sessionId'] = doc.id;
        sessionData.add(data);
      }

      final exportData = {
        'userId': userId,
        'exportTimestamp': DateTime.now().toIso8601String(),
        'dateRange': {
          'start': startDate.toIso8601String(),
          'end': endDate.toIso8601String(),
        },
        'totalSessions': sessionData.length,
        'sessions': sessionData,
      };

      final filename = 'sessions_${userId}_${startDate.millisecondsSinceEpoch}_${endDate.millisecondsSinceEpoch}';
      
      if (format.toLowerCase() == 'json') {
        await saveJsonToFile(exportData, filename);
      } else if (format.toLowerCase() == 'csv') {
        await _saveSessionsCsv(exportData, filename);
      }
    } catch (e) {
      rethrow;
    }
  }

  /// Export current session data
  Future<void> exportCurrentSession(String userId, BehavioralData data) async {
    final sessionData = {
      'userId': userId,
      'exportTimestamp': DateTime.now().toIso8601String(),
      'sessionType': 'current',
      'behavioralData': data.toJson(),
    };

    final filename = 'current_session_${userId}_${DateTime.now().millisecondsSinceEpoch}';
    
    // Export both formats
    await saveJsonToFile(sessionData, filename);
    await _saveCurrentSessionCsv(sessionData, filename);
  }

  Future<void> _saveSessionsCsv(Map<String, dynamic> data, String filename) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/$filename.csv');

    final csvBuffer = StringBuffer();
    
    // CSV Header
    csvBuffer.writeln('SessionId,Timestamp,KeystrokeIntervals,AvgTapPressure,AvgSwipeVelocity,SessionDuration,TypingSpeedKPM,TapCount,SwipeCount');
    
    // CSV Data
    final sessions = data['sessions'] as List;
    for (var session in sessions) {
      final keystrokeIntervals = session['keystroke_intervals'] ?? [];
      final tapPositions = session['tap_positions'] ?? [];
      final swipeDurations = session['swipe_durations'] ?? [];
      
      csvBuffer.writeln([
        session['sessionId'] ?? '',
        session['timestamp'] ?? '',
        keystrokeIntervals.length,
        session['avg_tap_pressure'] ?? 0,
        session['avg_swipe_velocity'] ?? 0,
        session['session_duration'] ?? 0,
        session['typing_speed_kpm'] ?? 0,
        tapPositions.length,
        swipeDurations.length,
      ].join(','));
    }

    await file.writeAsString(csvBuffer.toString());
  }

  Future<void> _saveCurrentSessionCsv(Map<String, dynamic> data, String filename) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/$filename.csv');

    final csvBuffer = StringBuffer();
    final behavioralData = data['behavioralData'] as Map<String, dynamic>;
    
    // Add headers and data
    csvBuffer.writeln('Metric,Value');
    behavioralData.forEach((key, value) {
      csvBuffer.writeln('$key,${value.toString()}');
    });

    await file.writeAsString(csvBuffer.toString());
  }

  /// Get export statistics
  Future<Map<String, dynamic>> getExportStats(String userId) async {
    try {
      final sessions = await _firestore
          .collection('users')
          .doc(userId)
          .collection('sessions')
          .get();

      final directory = await getApplicationDocumentsDirectory();
      final files = directory.listSync();
      final userFiles = files.where((file) => file.path.contains(userId)).toList();

      return {
        'totalSessions': sessions.docs.length,
        'exportedFiles': userFiles.length,
        'lastExportPath': directory.path,
      };
    } catch (e) {
      return {
        'totalSessions': 0,
        'exportedFiles': 0,
        'lastExportPath': '',
        'error': e.toString(),
      };
    }
  }
}
