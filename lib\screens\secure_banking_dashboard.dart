import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import '../services/enterprise_security_service.dart';
import '../services/real_behavioral_engine.dart';
import '../services/fraud_detection_service.dart';
import '../services/advanced_encryption_service.dart';

class SecureBankingDashboard extends StatefulWidget {
  const SecureBankingDashboard({super.key});

  @override
  State<SecureBankingDashboard> createState() => _SecureBankingDashboardState();
}

class _SecureBankingDashboardState extends State<SecureBankingDashboard> with TickerProviderStateMixin {
  final EnterpriseSecurityService _securityService = EnterpriseSecurityService();
  final RealBehavioralEngine _behavioralEngine = RealBehavioralEngine();
  final FraudDetectionService _fraudService = FraudDetectionService();
  final AdvancedEncryptionService _encryptionService = AdvancedEncryptionService();
  
  late AnimationController _securityAnimation;
  late AnimationController _threatAnimation;
  
  bool _isSecureSession = false;
  bool _biometricVerified = false;
  SecurityThreatLevel _currentThreatLevel = SecurityThreatLevel.low;
  String _authenticatedUser = '';
  double _behavioralConfidence = 0.0;
  List<SecurityEvent> _recentSecurityEvents = [];
  Map<String, dynamic> _systemSecurityStatus = {};
  
  @override
  void initState() {
    super.initState();
    _initializeSecuritySystems();
    _setupAnimations();
    _startContinuousMonitoring();
  }

  @override
  void dispose() {
    _securityAnimation.dispose();
    _threatAnimation.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    _securityAnimation = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
    
    _threatAnimation = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
  }

  Future<void> _initializeSecuritySystems() async {
    await _securityService.initialize();
    _behavioralEngine.initialize();
    await _fraudService.initialize();
    await _encryptionService.initialize();
    
    // Enable hardware security features
    await _enableHardwareSecurity();
    
    setState(() {
      _systemSecurityStatus = {
        'encryption': 'AES-256-GCM Active',
        'biometric': 'Fingerprint + Face ID Ready',
        'behavioral': 'ML Engine Running',
        'fraud_detection': 'Real-time Monitoring',
        'network_security': 'SSL Pinning + OWASP',
        'device_integrity': 'Root/Jailbreak Detection',
      };
    });
  }

  Future<void> _enableHardwareSecurity() async {
    // Enable Android hardware security features
    await SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    
    // Set security flags
    await SystemChannels.platform.invokeMethod('SystemChrome.setApplicationSwitcherDescription', {
      'label': 'Secure Banking Session',
      'primaryColor': 0xFF000000, // Black for security
    });
  }

  void _startContinuousMonitoring() {
    // Real-time security monitoring
    Timer.periodic(const Duration(seconds: 5), (timer) {
      _performSecurityCheck();
    });
    
    // Behavioral pattern monitoring
    Timer.periodic(const Duration(seconds: 2), (timer) {
      _updateBehavioralConfidence();
    });
    
    // Fraud detection monitoring
    Timer.periodic(const Duration(seconds: 10), (timer) {
      _checkForFraudulentActivity();
    });
  }

  void _performSecurityCheck() async {
    final securityLevel = await _securityService.getCurrentThreatLevel();
    final newEvents = await _securityService.getRecentSecurityEvents();
    
    setState(() {
      _currentThreatLevel = securityLevel;
      _recentSecurityEvents = newEvents;
    });
    
    if (securityLevel == SecurityThreatLevel.critical) {
      _triggerSecurityAlert();
    }
  }

  void _updateBehavioralConfidence() async {
    final confidence = _behavioralEngine.getCurrentConfidence();
    setState(() {
      _behavioralConfidence = confidence;
    });
    
    if (confidence < 0.3) {
      _triggerBehavioralAlert();
    }
  }

  void _checkForFraudulentActivity() async {
    final riskScore = await _fraudService.calculateRiskScore();
    if (riskScore > 0.8) {
      _triggerFraudAlert();
    }
  }

  void _triggerSecurityAlert() {
    _threatAnimation.forward();
    HapticFeedback.heavyImpact();
    
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.security, color: Colors.red),
            SizedBox(width: 8),
            Text('Security Alert'),
          ],
        ),
        content: const Text('Critical security threat detected. Session will be terminated for your protection.'),
        actions: [
          TextButton(
            onPressed: () => _emergencyLogout(),
            child: const Text('Secure Logout'),
          ),
        ],
      ),
    );
  }

  void _triggerBehavioralAlert() {
    HapticFeedback.mediumImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('⚠️ Behavioral patterns deviate from normal usage'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _triggerFraudAlert() {
    HapticFeedback.heavyImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('🚨 Potential fraudulent activity detected'),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _emergencyLogout() {
    // Wipe sensitive data
    _securityService.emergencyDataWipe();
    Navigator.of(context).popUntil((route) => route.isFirst);
  }

  Future<void> _performBiometricAuthentication() async {
    try {
      final result = await _securityService.authenticateBiometric();
      setState(() {
        _biometricVerified = result;
      });
      
      if (result) {
        HapticFeedback.lightImpact();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ Biometric authentication successful'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('❌ Biometric authentication failed: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0A0E27),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              _buildSecurityHeader(),
              _buildThreatLevelIndicator(),
              _buildBehavioralMonitoring(),
              _buildSecurityControls(),
              _buildTransactionSecurity(),
              _buildSystemStatus(),
              _buildSecurityLogs(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSecurityHeader() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.blue.shade900,
            Colors.indigo.shade800,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withOpacity(0.3),
            blurRadius: 20,
            spreadRadius: 5,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              AnimatedBuilder(
                animation: _securityAnimation,
                builder: (context, child) {
                  return Transform.rotate(
                    angle: _securityAnimation.value * 2 * 3.14159,
                    child: const Icon(
                      Icons.security,
                      color: Colors.white,
                      size: 32,
                    ),
                  );
                },
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Canara Bank SecureCore',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'Enterprise Cyber Security Suite',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: _isSecureSession ? Colors.green : Colors.red,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  _isSecureSession ? 'SECURE' : 'UNSECURED',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              _buildSecurityStat('Encryption', 'AES-256', Icons.lock),
              _buildSecurityStat('Threat Level', _getThreatLevelText(), Icons.shield),
              _buildSecurityStat('Behavioral', '${(_behavioralConfidence * 100).toInt()}%', Icons.psychology),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityStat(String label, String value, IconData icon) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(icon, color: Colors.white, size: 20),
            const SizedBox(height: 4),
            Text(
              value,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 10,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThreatLevelIndicator() {
    Color threatColor = _getThreatLevelColor();
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: threatColor.withOpacity(0.1),
        border: Border.all(color: threatColor, width: 2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          AnimatedBuilder(
            animation: _threatAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: 1.0 + (_threatAnimation.value * 0.2),
                child: Icon(
                  _getThreatLevelIcon(),
                  color: threatColor,
                  size: 32,
                ),
              );
            },
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Security Threat Level: ${_getThreatLevelText()}',
                  style: TextStyle(
                    color: threatColor,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  _getThreatLevelDescription(),
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBehavioralMonitoring() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.purple.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.psychology, color: Colors.purple, size: 24),
              SizedBox(width: 8),
              Text(
                'Behavioral Biometrics',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          LinearProgressIndicator(
            value: _behavioralConfidence,
            backgroundColor: Colors.grey.shade800,
            valueColor: AlwaysStoppedAnimation<Color>(
              _behavioralConfidence > 0.7 ? Colors.green : 
              _behavioralConfidence > 0.4 ? Colors.orange : Colors.red,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Confidence: ${(_behavioralConfidence * 100).toStringAsFixed(1)}%',
            style: const TextStyle(color: Colors.white70),
          ),
          const SizedBox(height: 8),
          Text(
            _getBehavioralStatusText(),
            style: TextStyle(
              color: _behavioralConfidence > 0.7 ? Colors.green : 
                     _behavioralConfidence > 0.4 ? Colors.orange : Colors.red,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityControls() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Security Controls',
            style: TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildSecurityButton(
                  'Biometric Auth',
                  Icons.fingerprint,
                  _biometricVerified ? Colors.green : Colors.blue,
                  _performBiometricAuthentication,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildSecurityButton(
                  'Emergency Lock',
                  Icons.lock,
                  Colors.red,
                  _emergencyLogout,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildSecurityButton(
                  'Secure Session',
                  Icons.security,
                  Colors.orange,
                  () => _toggleSecureSession(),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildSecurityButton(
                  'Encrypt Data',
                  Icons.enhanced_encryption,
                  Colors.purple,
                  () => _performDataEncryption(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityButton(String title, IconData icon, Color color, VoidCallback onPressed) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      child: Column(
        children: [
          Icon(icon, size: 24),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionSecurity() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.green.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.account_balance, color: Colors.green, size: 24),
              SizedBox(width: 8),
              Text(
                'Transaction Security',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildTransactionControl('Fund Transfer', '₹2,50,000 limit'),
          _buildTransactionControl('Bill Payment', '₹50,000 limit'),
          _buildTransactionControl('Investment', '₹10,00,000 limit'),
        ],
      ),
    );
  }

  Widget _buildTransactionControl(String type, String limit) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            type,
            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
          ),
          Text(
            limit,
            style: const TextStyle(color: Colors.green),
          ),
        ],
      ),
    );
  }

  Widget _buildSystemStatus() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'System Security Status',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ..._systemSecurityStatus.entries.map((entry) => 
            _buildStatusItem(entry.key, entry.value),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusItem(String key, String value) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            key.replaceAll('_', ' ').toUpperCase(),
            style: const TextStyle(color: Colors.white70, fontSize: 12),
          ),
          Text(
            value,
            style: const TextStyle(color: Colors.green, fontSize: 12, fontWeight: FontWeight.w600),
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityLogs() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Recent Security Events',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ..._recentSecurityEvents.take(5).map((event) => 
            _buildSecurityLogItem(event),
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityLogItem(SecurityEvent event) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: _getEventColor(event.severity).withOpacity(0.1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        children: [
          Icon(
            _getEventIcon(event.type),
            color: _getEventColor(event.severity),
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              event.message,
              style: const TextStyle(color: Colors.white, fontSize: 12),
            ),
          ),
          Text(
            _formatTime(event.timestamp),
            style: const TextStyle(color: Colors.white54, fontSize: 10),
          ),
        ],
      ),
    );
  }

  // Helper methods
  String _getThreatLevelText() {
    switch (_currentThreatLevel) {
      case SecurityThreatLevel.low:
        return 'LOW';
      case SecurityThreatLevel.medium:
        return 'MEDIUM';
      case SecurityThreatLevel.high:
        return 'HIGH';
      case SecurityThreatLevel.critical:
        return 'CRITICAL';
    }
  }

  Color _getThreatLevelColor() {
    switch (_currentThreatLevel) {
      case SecurityThreatLevel.low:
        return Colors.green;
      case SecurityThreatLevel.medium:
        return Colors.yellow;
      case SecurityThreatLevel.high:
        return Colors.orange;
      case SecurityThreatLevel.critical:
        return Colors.red;
    }
  }

  IconData _getThreatLevelIcon() {
    switch (_currentThreatLevel) {
      case SecurityThreatLevel.low:
        return Icons.security;
      case SecurityThreatLevel.medium:
        return Icons.warning;
      case SecurityThreatLevel.high:
        return Icons.error;
      case SecurityThreatLevel.critical:
        return Icons.dangerous;
    }
  }

  String _getThreatLevelDescription() {
    switch (_currentThreatLevel) {
      case SecurityThreatLevel.low:
        return 'All systems secure. Normal operations.';
      case SecurityThreatLevel.medium:
        return 'Minor security concerns detected.';
      case SecurityThreatLevel.high:
        return 'Elevated security threats present.';
      case SecurityThreatLevel.critical:
        return 'IMMEDIATE ACTION REQUIRED!';
    }
  }

  String _getBehavioralStatusText() {
    if (_behavioralConfidence > 0.8) {
      return 'User behavior matches baseline perfectly';
    } else if (_behavioralConfidence > 0.6) {
      return 'Minor deviations in behavioral patterns';
    } else if (_behavioralConfidence > 0.4) {
      return 'Moderate behavioral anomalies detected';
    } else {
      return 'ALERT: Significant behavioral deviation';
    }
  }

  Color _getEventColor(SecurityEventSeverity severity) {
    switch (severity) {
      case SecurityEventSeverity.info:
        return Colors.blue;
      case SecurityEventSeverity.warning:
        return Colors.orange;
      case SecurityEventSeverity.error:
        return Colors.red;
    }
  }

  IconData _getEventIcon(SecurityEventType type) {
    switch (type) {
      case SecurityEventType.authentication:
        return Icons.login;
      case SecurityEventType.transaction:
        return Icons.payment;
      case SecurityEventType.behavioral:
        return Icons.psychology;
      case SecurityEventType.network:
        return Icons.network_check;
      case SecurityEventType.system:
        return Icons.computer;
    }
  }

  String _formatTime(DateTime timestamp) {
    final now = DateTime.now();
    final diff = now.difference(timestamp);
    
    if (diff.inMinutes < 1) {
      return 'Now';
    } else if (diff.inMinutes < 60) {
      return '${diff.inMinutes}m ago';
    } else {
      return '${diff.inHours}h ago';
    }
  }

  void _toggleSecureSession() {
    setState(() {
      _isSecureSession = !_isSecureSession;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(_isSecureSession ? '🔒 Secure session activated' : '🔓 Secure session deactivated'),
        backgroundColor: _isSecureSession ? Colors.green : Colors.orange,
      ),
    );
  }

  void _performDataEncryption() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('🔐 Data encryption initiated with AES-256-GCM'),
        backgroundColor: Colors.purple,
      ),
    );
  }
}
